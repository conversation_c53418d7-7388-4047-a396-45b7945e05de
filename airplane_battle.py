import pygame
import random
import sys

# Initialize Pygame
pygame.init()

# Game constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
GREEN = (0, 255, 0)

class Player:
    def __init__(self):
        self.width = 50
        self.height = 40
        self.x = SCREEN_WIDTH // 2 - self.width // 2
        self.y = SCREEN_HEIGHT - 80
        self.speed = 5
        self.bullets = []
        
    def move(self, keys):
        if keys[pygame.K_LEFT] and self.x > 0:
            self.x -= self.speed
        if keys[pygame.K_RIGHT] and self.x < SCREEN_WIDTH - self.width:
            self.x += self.speed
        if keys[pygame.K_UP] and self.y > 0:
            self.y -= self.speed
        if keys[pygame.K_DOWN] and self.y < SCREEN_HEIGHT - self.height:
            self.y += self.speed
            
    def shoot(self):
        bullet = Bullet(self.x + self.width // 2, self.y, -10)
        self.bullets.append(bullet)
        
    def draw(self, screen):
        pygame.draw.rect(screen, BLUE, (self.x, self.y, self.width, self.height))
        # Draw bullets
        for bullet in self.bullets[:]:
            bullet.move()
            bullet.draw(screen)
            if bullet.y < 0:
                self.bullets.remove(bullet)

class Enemy:
    def __init__(self):
        self.width = 40
        self.height = 30
        self.x = random.randint(0, SCREEN_WIDTH - self.width)
        self.y = -self.height
        self.speed = random.randint(2, 5)
        
    def move(self):
        self.y += self.speed
        
    def draw(self, screen):
        pygame.draw.rect(screen, RED, (self.x, self.y, self.width, self.height))

class Bullet:
    def __init__(self, x, y, speed):
        self.x = x
        self.y = y
        self.speed = speed
        self.width = 5
        self.height = 10
        
    def move(self):
        self.y += self.speed
        
    def draw(self, screen):
        pygame.draw.rect(screen, GREEN, (self.x, self.y, self.width, self.height))

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("飞机大战")
        self.clock = pygame.time.Clock()
        self.player = Player()
        self.enemies = []
        self.score = 0
        self.font = pygame.font.Font(None, 36)
        self.enemy_spawn_timer = 0
        
    def spawn_enemy(self):
        if self.enemy_spawn_timer <= 0:
            self.enemies.append(Enemy())
            self.enemy_spawn_timer = random.randint(30, 60)
        else:
            self.enemy_spawn_timer -= 1
            
    def check_collisions(self):
        # Check bullet-enemy collisions
        for bullet in self.player.bullets[:]:
            for enemy in self.enemies[:]:
                if (bullet.x < enemy.x + enemy.width and
                    bullet.x + bullet.width > enemy.x and
                    bullet.y < enemy.y + enemy.height and
                    bullet.y + bullet.height > enemy.y):
                    self.player.bullets.remove(bullet)
                    self.enemies.remove(enemy)
                    self.score += 10
                    break
                    
        # Check player-enemy collisions
        for enemy in self.enemies:
            if (self.player.x < enemy.x + enemy.width and
                self.player.x + self.player.width > enemy.x and
                self.player.y < enemy.y + enemy.height and
                self.player.y + self.player.height > enemy.y):
                return True
        return False
        
    def run(self):
        running = True
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_SPACE:
                        self.player.shoot()
                        
            keys = pygame.key.get_pressed()
            self.player.move(keys)
            
            # Spawn enemies
            self.spawn_enemy()
            
            # Move enemies
            for enemy in self.enemies[:]:
                enemy.move()
                if enemy.y > SCREEN_HEIGHT:
                    self.enemies.remove(enemy)
                    
            # Check collisions
            if self.check_collisions():
                print(f"游戏结束! 最终得分: {self.score}")
                running = False
                
            # Draw everything
            self.screen.fill(BLACK)
            self.player.draw(self.screen)
            
            for enemy in self.enemies:
                enemy.draw(self.screen)
                
            # Draw score
            score_text = self.font.render(f"得分: {self.score}", True, WHITE)
            self.screen.blit(score_text, (10, 10))
            
            pygame.display.flip()
            self.clock.tick(FPS)
            
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = Game()
    game.run()